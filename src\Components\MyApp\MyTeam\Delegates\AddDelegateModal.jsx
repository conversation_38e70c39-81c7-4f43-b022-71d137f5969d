import React, { useState } from "react";
import CustomDropdown from "../../../Global/CustomDropdown";
import DateTimeInput from "../../../Temporary/DateTimeInput";
import Input from "../../../Global/Input/Input";

const AddDelegateModal = ({ isOpen, onClose, onAdd }) => {
  const [show, setShow] = useState(false);
  const [formData, setFormData] = useState({
    delegate: "",
    taskType: "",
    sendNotificationTo: "",
    startDate: "",
    endDate: "",
    reason: "",
  });

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onAdd(formData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Delegation</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Delegation Details 
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Delegate *
            </label>
            <div className="w-3/4">
              <input
                type="text"
                name="delegate"
                value={formData.delegate}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    Task To Delegate *
  </label>
  <div className="w-3/4">
    <CustomDropdown
      options={[
        { label: "Task 1", value: "task1" },
        { label: "Task 2", value: "task2" },
        // ... add more options as needed
      ]}
      placeholder="Select a task"
      value={formData.taskType}
      onSelect={(selectedValue) =>
        setFormData({ ...formData, taskType: selectedValue })
      }
    />
  </div>
</div>
<div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    Send Notification To
  </label>
  <div className="w-3/4">
    <CustomDropdown
      options={[
        { label: "User A", value: "userA" },
        { label: "User B", value: "userB" },
        // ... add more options as needed
      ]}
      placeholder="Select user"
      value={formData.sendNotificationTo}
      onSelect={(selectedValue) =>
        setFormData({ ...formData, sendNotificationTo: selectedValue })
      }
    />
  </div>
</div>
<div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    Start Date *
  </label>
  <div className="w-3/4">
    <DateTimeInput
      
      value={formData.startDate}
      onChange={(date) =>
        setFormData({ ...formData, startDate: date })
      }
      placeholder="Select start date & time"
    />
  </div>
</div>
<div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    End Date *
  </label>
  <div className="w-3/4">
    <DateTimeInput
      
      value={formData.endDate}
      onChange={(date) =>
        setFormData({ ...formData, endDate: date })
      }
      placeholder="Select end date & time"
    />
  </div>
</div>
<div className="flex mb-2 items-center">
                <label className="w-1/4">Reason *</label>
                <div className="w-3/4">
                  <Input
                    name="justification"
                    type="bubbles"
                    placeholder="Reason"
                    value={formData.reason}
                    height="94px"
                    bubbles={true}
                    bubbleOptions={[
                      "Lost Permanent Card",
                      "Forgot Permanent Card",
                    ]}
                    onChange={handleChange}
                  />
                </div>
              </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDelegateModal;
