import React, { useState } from "react";
import GenericTable from "../../../GenericTable"; // Import GenericTable
import CustomDropdown from "../../../Global/CustomDropdown"
import Input from "../../../Global/Input/Input";
import { toast } from "react-toastify";   
import 'react-toastify/dist/ReactToastify.css';

const AddAccessRequest = ({ onClose, onAddAccess }) => {
  const [show, setShow] = useState(false);
  const [formData, setFormData] = useState({
    startDate: "",
    duration: "",
    endDate: "",
    justification: "",
    accessAreas: [],
  });

  const accessAreasData = [
    { areaName: "Area Role A", type: "Area", approvalRequested: "Yes" },
    { areaName: "Area Role B", type: "Area", approvalRequested: "No" },
  ];

  const accessAreasColumns = [
    { name: "Area Name", selector: (row) => row.areaName, sortable: true },
    { name: "Type", selector: (row) => row.type },
    { name: "Approval Requested", selector: (row) => row.approvalRequested },
  ];

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form Data Submitted:", formData);
    toast.success("Card request added successfully!");
    onClose(); 
    onAddAccess({
      areaName: "New Area", // Replace with actual area name if needed
      type: "Area", // Replace with actual type if needed
      startDate: formData.startDate,
      endDate: formData.endDate,
      status: "Pending", // Default status for new requests
    });
    onClose(); // Close modal after submission
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Access Request</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Access Details
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Access Start Date
            </label>
            <div className="w-3/4">
              <input
                type="datetime-local"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Duration
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={[
                  { label: "1 Hour", value: "1 Hour" },
                  { label: "1 Day", value: "1 Day" },
                  { label: "1 Week", value: "1 Week" },
                ]}
                value={formData.duration}
                onSelect={(value) => handleChange({ target: { name: "duration", value } })}
                placeholder="Select Duration"
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Remove Date
            </label>
            <div className="w-3/4">
              <input
                type="datetime-local"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
              />
            </div>
          </div>
          <div className="flex mb-2 items-center">
            <label className="w-1/4">Justification</label>
            <div className="w-3/4">
              <Input
                name="justification"
                type="bubbles"
                placeholder="Justification"
                value={formData.justification}
                height="94px"
                bubbles={true}
                bubbleOptions={[
                  "Lost Permanent Card",
                  "Forgot Permanent Card",
                ]}
                onChange={handleChange}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Select & Add Access Area(s) *
            </label>
            <div className="w-3/4">
              <input
                type="text"
                placeholder="Enter Access End Date"
                name="endDate"


                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
              />
            </div>
          </div>
          <div className="mb-4">
            <h2 className="text-[20px] text-[#333333] font-medium pb-4">
              Select & Add Access Area(s)
            </h2>
            <GenericTable
              showAddButton={false}
              columns={accessAreasColumns}
              data={accessAreasData}
              fixedHeader
              fixedHeaderScrollHeight="200px"
              highlightOnHover
              striped
            />
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddAccessRequest;
