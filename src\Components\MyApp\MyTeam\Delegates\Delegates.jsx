import React, { useState, useMemo } from "react";
import GenericTable from "../../../GenericTable";
import AddDelegateModal from "./AddDelegateModal";

const Delegates = () => {
  const [myDelegatesSearchTerm, setMyDelegatesSearchTerm] = useState("");
  const [delegatesToMeSearchTerm, setDelegatesToMeSearchTerm] = useState("");
  const [myDelegatesData, setMyDelegatesData] = useState([
    {
      name: "DAVID STOLLER",
      eid: "422353",
      taskType: "Access Area Audit",
      sendNotificationTo: "Delegate & Owner",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
    },
    {
      name: "STEVEN KRUSCHE",
      eid: "3545",
      taskType: "Meeting Site",
      sendNotificationTo: "Delegate & Owner",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
    },
    // ...other rows...
  ]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const delegatesToMeData = [
    {
      name: "DAVID STOLLER",
      eid: "422353",
      taskType: "Access Area Audit",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
      status: "Expired",
    },
    {
      name: "STEVEN KRUSCHE",
      eid: "3545",
      taskType: "Meeting Site",
      startDate: "19-Mar-2025",
      endDate: "19-Mar-2025",
      status: "Expired",
    },
    // ...other rows...
  ];

  const myDelegatesColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Task Type", selector: (row) => row.taskType },
    { name: "Send Notification To", selector: (row) => row.sendNotificationTo },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
  ];

  const delegatesToMeColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Task Type", selector: (row) => row.taskType },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
    { name: "Status", selector: (row) => row.status },
  ];

  const filteredMyDelegatesData = useMemo(() => {
    if (!myDelegatesSearchTerm) return myDelegatesData;
    return myDelegatesData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(myDelegatesSearchTerm.toLowerCase())
      )
    );
  }, [myDelegatesData, myDelegatesSearchTerm]);

  const filteredDelegatesToMeData = useMemo(() => {
    if (!delegatesToMeSearchTerm) return delegatesToMeData;
    return delegatesToMeData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(delegatesToMeSearchTerm.toLowerCase())
      )
    );
  }, [delegatesToMeData, delegatesToMeSearchTerm]);

  const handleAddDelegate = (newDelegate) => {
    setMyDelegatesData([
      {
        name: newDelegate.delegate,
        eid: "New", // Replace with actual EID logic
        taskType: newDelegate.taskType,
        sendNotificationTo: newDelegate.sendNotificationTo,
        startDate: newDelegate.startDate,
        endDate: newDelegate.endDate,
      },
      ...myDelegatesData, // Prepend new data
    ]);
  };

  return (
    <div>
      <div className="mb-4 bg-white rounded-[10px]">
        <GenericTable
          title={"My Delegates"}
          onAdd={() => setIsModalOpen(true)} // Updated to open AddDelegateModal
          searchTerm={myDelegatesSearchTerm}
          onSearchChange={(e) => setMyDelegatesSearchTerm(e.target.value)}
          columns={myDelegatesColumns}
          data={filteredMyDelegatesData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
          showAddButton={true}
          onAddButtonClick={() => setIsModalOpen(true)}
        />
      </div>
      <AddDelegateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAdd={handleAddDelegate}
      />
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title={"Delegates to Me"}
          showAddButton={false}
          searchTerm={delegatesToMeSearchTerm}
          onSearchChange={(e) => setDelegatesToMeSearchTerm(e.target.value)}
          columns={delegatesToMeColumns}
          data={filteredDelegatesToMeData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
    </div>
  );
};

export default Delegates;
