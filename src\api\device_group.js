import api from "./";

/**
 * Get all device group settings for a kiosk group (paginated).
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @returns {Promise<any>} A promise that resolves to the list of device group settings.
 */
export const getDeviceGroups = async (kioskGroupId) => {
  const response = await api.get(`/facility/device-groups/${kioskGroupId}`);
  return response.data;
};

/**
 * Create a device group setting for a kiosk group.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {object} groupData - Device group setting data to create.
 * @returns {Promise<any>} A promise that resolves to the created device group setting.
 */
export const createDeviceGroups = async (kioskGroupId, groupData) => {
  console.log("API call: Creating device group with data:", groupData);
  const response = await api.post(`/facility/device-groups/${kioskGroupId}`, groupData);
  return response.data;
};

/**
 * Get a device group setting by ID for a kiosk group.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {string} kioskGroupSettingId - The ID of the specific setting.
 * @returns {Promise<any>} A promise that resolves to the device group setting.
 */
export const getDeviceGroupById = async (kioskGroupId, kioskGroupSettingId) => {
  const response = await api.get(`/facility/device-groups/${kioskGroupId}/${kioskGroupSettingId}`);
  return response.data;
};

/**
 * Update a single device group setting's details.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {string} kioskGroupSettingId - The ID of the device group setting.
 * @param {object} updateData - The data to update the setting with.
 * @returns {Promise<any>} A promise that resolves to the updated device group setting.
 */
export const updateDeviceGroup = async (kioskGroupId, kioskGroupSettingId, updateData) => {
  const response = await api.patch(`/facility/device-groups/${kioskGroupId}/${kioskGroupSettingId}`, updateData);
  return response.data;
};

/**
 * Delete a device group setting by ID.
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {string} kioskGroupSettingId - The ID of the device group setting.
 * @returns {Promise<any>} A promise that resolves to the deletion confirmation.
 */
export const deleteDeviceGroup = async (kioskGroupId, kioskGroupSettingId) => {
  const response = await api.delete(`/facility/device-groups/${kioskGroupId}/${kioskGroupSettingId}`);
  return response.data;
};

/**
 * Update multiple device group settings for a kiosk group (bulk).
 * @param {string} kioskGroupId - The ID of the kiosk group.
 * @param {object[]} bulkUpdateData - Array of settings to update.
 * @returns {Promise<any>} A promise that resolves to the updated settings.
 */
export const bulkUpdateDeviceGroups = async (kioskGroupId, bulkUpdateData) => {
  const response = await api.patch(`/facility/device-groups/${kioskGroupId}/bulk`, bulkUpdateData);
  return response.data;
};
