import React, { useState } from "react";

import GenericTable from "../../../GenericTable";



const ViewAccess = ({ onClose }) => {
  const [show, setShow] = useState(false);
  const [activeTab, setActiveTab] = useState("Facility");

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);



  const FacilityData = [

    { facilityName: "John Doe", facilityId: "E123", address: "ASDF", city: "ABCD", state: "ABC Corp", country: "India" },

    { facilityName: "Jane Smith", facilityId: "E124", address: "SDFGH", city: "WXYZ", state: "XYZ Ltd", country: "USA" },

  ];



  const readersData = [

    { name: "<PERSON>", eid: "E123", role: "<PERSON><PERSON>", owner: "Yes", company: "ABC Corp", phone: "1234567890", email: "<EMAIL>" },
    { name: "<PERSON>", eid: "E124", role: "User", owner: "No", company: "XYZ Ltd", phone: "9876543210", email: "<EMAIL>" },

  ];



  const residentsData = [

    { name: "<PERSON>", readerId: "101", controller: "1234567890", date: "Mar-03-2025" },

    { name: "Bob Brown", readerId: "102", controller: "9876543210", date: "Jan-03-2023" },

  ];



  const FacilityColumns = [

    { name: "Facility Name", selector: (row) => row.facilityName, sortable: true },

    { name: "Facility ID", selector: (row) => row.facilityId },

    { name: "Address", selector: (row) => row.address },

    { name: "City", selector: (row) => row.city },

    { name: "State", selector: (row) => row.state },

    { name: "Country", selector: (row) => row.country },



  ];



  const readersColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Role", selector: (row) => row.role },
    { name: "Owner", selector: (row) => row.owner },
    { name: "Company", selector: (row) => row.company },
    { name: "Phone", selector: (row) => row.phone },
    { name: "Email", selector: (row) => row.email },

  ];



  const residentsColumns = [

    { name: "Reader Name", selector: (row) => row.name, sortable: true },

    { name: "Reader ID", selector: (row) => row.readerId },

    { name: "Controller", selector: (row) => row.controller },

    { name: "Added on", selector: (row) => row.date },

  ];



  return (

    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View Area Details</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>



        {/* Fields Section */}

        <div className="px-6 py-4">

          <div className="grid grid-cols-1 gap-4">

            <div className="flex items-center">

              <p className="text-sm font-medium text-gray-600 w-1/3">Name</p>

              <p className="text-base text-gray-800">Main Building, 1st Floor</p>

            </div>

            <div className="flex items-center">

              <p className="text-sm font-medium text-gray-600 w-1/3">Type</p>

              <p className="text-base text-gray-800 ">Administrative</p>

            </div>

            <div className="flex items-center">

              <p className="text-sm font-medium text-gray-600 w-1/3">Access Type</p>

              <p className="text-base text-gray-800">Online</p>

            </div>

            <div className="flex items-center">

              <p className="text-sm font-medium text-gray-600 w-1/3">Owner</p>

              <p className="text-base text-gray-800">MultiTech General</p>

            </div>

          </div>

        </div>



        {/* Tab Navigation */}

        <div className="flex gap-2 ms-5 mt-4">

          <button



            className={`w-1/5

              py-2 rounded-full

             font-medium

             ${activeTab === "Facility"

                ? "bg-[#4F2683] text-white"              // Filled (active)

                : "border border-[#4F2683] text-[#4F2683] bg-[#EAE0F6]" // Outlined (inactive)

              }

           `}

            onClick={() => setActiveTab("Facility")}

          >

            Facility(s)

          </button>

          <button



            className={`w-1/5

              py-2 rounded-full

             font-medium

             ${activeTab === "Readers"

                ? "bg-[#4F2683] text-white"              // Filled (active)

                : "border border-[#4F2683] text-[#4F2683] bg-[#EAE0F6]" // Outlined (inactive)

              }

           `}

            onClick={() => setActiveTab("Readers")}

          >

            Readers

          </button>

          <button



            className={`w-1/5

              py-2 rounded-full

             font-medium

             ${activeTab === "Residents"

                ? "bg-[#4F2683] text-white"              // Filled (active)

                : "border border-[#4F2683] text-[#4F2683] bg-[#EAE0F6]" // Outlined (inactive)

              }

           `}

            onClick={() => setActiveTab("Residents")}

          >

            Residents

          </button>

        </div>



        {/* Table Content */}

        <div className="p-6">

          {activeTab === "Facility" && (

            <GenericTable

              showAddButton={false}

              columns={FacilityColumns}

              data={FacilityData}

              fixedHeader

              fixedHeaderScrollHeight="400px"

              highlightOnHover

              striped

            />

          )}

          {activeTab === "Readers" && (

            <GenericTable

              showAddButton={false}

              columns={readersColumns}

              data={readersData}

              fixedHeader

              fixedHeaderScrollHeight="400px"

              highlightOnHover

              striped

            />

          )}

          {activeTab === "Residents" && (

            <GenericTable

              showAddButton={false}

              columns={residentsColumns}

              data={residentsData}

              fixedHeader

              fixedHeaderScrollHeight="400px"

              highlightOnHover

              striped

            />

          )}

        </div>

      </div>

    </div>
  );
};

export default ViewAccess;

