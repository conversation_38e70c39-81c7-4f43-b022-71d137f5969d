import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import GenericTable from "../../Components/GenericTable";
import AddAccessRequest from "../../Components/MyApp/MyProfile/Access/AddAccessRequest";
import ViewAccess from "../../Components/MyApp/MyAccess/ViewAccess";
import ViewReport from "../../Components/MyApp/MyAccess/ViewAreaAssignments";
import MyHistory from "../../Components/MyApp/MyAccess/Myhistory"; // Import MyHistory
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import TruncatedRow from "../../Components/Tooltip/TrucantedRow";
import viewicon from "../../Images/ViewIcon.svg";
import report from "../../Images/ReportList.svg";

const MyAccessAreas = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(t("my_access_areas.tabs.my_areas"));
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [selectedGuest, setSelectedGuest] = useState(null);

  const [accessData, setAccessData] = useState([
    {
      areaName: "BANGALORE, IND [BLR] GX GENERAL ACCESS 24HR",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "BERLIN, GER [BER10] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "DEL - SANTA CLARA CA USA [SCA08] - SECURITY OFFICE AREA",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "DUSSELDORF, GER [DUS02] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "HAMBURG, GER [HAM02] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "LIMA, PER [LIM01] - GENERAL ACCESS 24 HR",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
  ]);

  const identitiesData = [
    {
      identity: "AKSHAY SARDANA",
      eid: "2432",
      type: "Employee",
      company: "GFS",
      supervisor: "STEVEN KRUSCHKE",
      startDate: "Jan-1-2021",
      endDate: "Jan-1-2021",
      status: "Assigned",
    },
  ];

  const handleAddAccess = (newAccess) => {
    setAccessData((prevData) => [...prevData, newAccess]);
  };

  const columnsMyAreas = [
    {
      name: <TruncatedCell text={t("my_access_areas.area_name")} />,
      selector: (row) => row.areaName,
      cell: (row) => <TruncatedRow text={row.areaName} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.type")} />,
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.start_date")} />,
      selector: (row) => row.startDate,
      cell: (row) => <TruncatedRow text={row.startDate} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.end_date")} />,
      selector: (row) => row.endDate,
      cell: (row) => <TruncatedRow text={row.endDate} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.status")} />,
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: <TruncatedCell text={t("my_access_areas.action")} />,
      cell: () => (
        <div className="flex justify-center items-center space-x-2">
          <div className="relative group">
            <img
              src={viewicon}
              alt={t("my_access_areas.view")}
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
              onClick={() => setIsViewModalOpen(true)}
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t("my_access_areas.view_area_details")}
            </div>
          </div>

          <div className="relative group">
            <img
              src={report}
              alt={t("my_access_areas.report")}
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
              onClick={() => setIsReportModalOpen(true)}
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t("my_access_areas.view_area_assignments")}
            </div>
          </div>
        </div>
      ),
    },
  ];

  const columnsIdentities = [
    {
      name: <TruncatedCell text={t("my_access_areas.identity")} />,
      selector: (row) => row.identity,
      cell: (row) => <TruncatedRow text={row.identity} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.eid")} />,
      selector: (row) => row.eid,
      cell: (row) => <TruncatedRow text={row.eid} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.type")} />,
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.company")} />,
      selector: (row) => row.company,
      cell: (row) => <TruncatedRow text={row.company} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.supervisor")} />,
      selector: (row) => row.supervisor,
      cell: (row) => <TruncatedRow text={row.supervisor} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.start_date")} />,
      selector: (row) => row.startDate,
      cell: (row) => <TruncatedRow text={row.startDate} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.end_date")} />,
      selector: (row) => row.endDate,
      cell: (row) => <TruncatedRow text={row.endDate} />,
    },
    {
      name: <TruncatedCell text={t("my_access_areas.status")} />,
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-full ${
            row.status === "Assigned"
              ? "bg-green-100 text-green-600"
              : "bg-blue-100 text-blue-600"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: <TruncatedCell text={t("my_access_areas.action")} />,
      cell: (row) => (
        <div className="flex justify-center items-center space-x-2">
          <div className="relative group">
            <img
              src={viewicon}
              alt={t("my_access_areas.view")}
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
              onClick={() => {
                setSelectedGuest(row);
                setIsHistoryModalOpen(true);
              }}
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t("my_access_areas.view_request_list")}
            </div>
          </div>
        </div>
      ),
    },
  ];

  const filteredData = useMemo(() => {
    if (!tableSearchTerm) return activeTab === t("my_access_areas.tabs.my_areas") ? accessData : identitiesData;
    return (activeTab === t("my_access_areas.tabs.my_areas") ? accessData : identitiesData).filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(tableSearchTerm.toLowerCase())
      )
    );
  }, [accessData, identitiesData, tableSearchTerm, activeTab]);

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      {/* Page Heading */}
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">My Access Areas</h2>
      </div>
      {/* Tabs */}
      <div className="flex space-x-4 mb-4">
        <button
          className={`px-4 py-2 rounded-full ${
            activeTab === t("my_access_areas.tabs.my_areas")
              ? "bg-[#4F2683] text-white"
              : "border border-[#4F2683] text-[#4F2683] bg-white"
          }`}
          onClick={() => setActiveTab(t("my_access_areas.tabs.my_areas"))}
        >
          {t("my_access_areas.tabs.my_areas")}
        </button>
        <button
          className={`px-4 py-2 rounded-full ${
            activeTab === t("my_access_areas.tabs.identities_with_my_areas")
              ? "bg-[#4F2683] text-white"
              : "border border-[#4F2683] text-[#4F2683] bg-white"
          }`}
          onClick={() => setActiveTab(t("my_access_areas.tabs.identities_with_my_areas"))}
        >
          {t("my_access_areas.tabs.identities_with_my_areas")}
        </button>
      </div>

      {/* Table */}
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title={activeTab}
          searchTerm={tableSearchTerm}
          onSearchChange={(e) => setTableSearchTerm(e.target.value)}
          columns={activeTab === t("my_access_areas.tabs.my_areas") ? columnsMyAreas : columnsIdentities}
          data={filteredData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>

      {/* Modals */}
      {isAddModalOpen && (
        <AddAccessRequest
          onClose={() => setIsAddModalOpen(false)}
          onAddAccess={handleAddAccess}
        />
      )}
      {isViewModalOpen && <ViewAccess onClose={() => setIsViewModalOpen(false)} />}
      {isReportModalOpen && <ViewReport onClose={() => setIsReportModalOpen(false)} />}
      {isHistoryModalOpen && (
        <MyHistory
          guest={selectedGuest}
          onClose={() => setIsHistoryModalOpen(false)}
        />
      )}
    </div>
  );
};

export default MyAccessAreas;