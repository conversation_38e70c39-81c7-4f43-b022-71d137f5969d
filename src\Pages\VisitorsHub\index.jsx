import React, { useState, useRef, useEffect, useCallback } from "react";

import DateInput from "../../Components/Global/Input/ValidationHubDate";
import GenericTable from "../../Components/GenericTable";
import DetailsCard from "../../Components/Global/DetailsCard";
import PrintModal from "../../Components/Global/PrintModal";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import HostSearch from "./HostSearch";
import VisitorSearch from "./VisitorSearch";
import useClickOutside from "../InpatientVisit/useClickOutside";
import Button from "../../Components/Global/Button";
import VisitorForm from "../../Components/Global/Forms/VisitorForm";
import defaultImg from "../../Images/demoimg.svg"
import moment from "moment";
import { FilterButtons } from "../../Components/GenericTable";
import { getVisitorColumns } from "../../api/tableDataColumns"; 
import homeicon from "../../Images/home-icon.svg";
import CreateVisitorModal from "../../Components/VisitorHub/CreateVisitorModal";
import formatDateTime from "../../utils/formatDate";
import { searchVisits, getVisitGuests } from "../../api/visitor-hub";
import Loader from "../../Components/Loader";

const ReceptionDesk = () => {
  // ---------------- State Variables ----------------
  const [searchTerm, setSearchTerm] = useState("");
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [guestSearchTerm, setGuestSearchTerm] = useState("");
  const [guestSearchResults, setGuestSearchResults] = useState([]);
  const [isGuestDropdownVisible, setIsGuestDropdownVisible] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [selectedHost, setSelectedHost] = useState(null);
  const [allGuests, setAllGuests] = useState([]);
  const [selectedGuest, setSelectedGuest] = useState(null);
  const [dateFilteredGuests, setDateFilteredGuests] = useState([]);
  const [activeFilter, setActiveFilter] = useState("all");
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedGuestId, setSelectedGuestId] = useState(null);
  const [showVisitorForm, setShowVisitorForm] = useState(false);
  const [patientSearchPlaceholder, setPatientSearchPlaceholder] = useState("Search By Host Name, EID");
  const [guestSearchPlaceholder, setGuestSearchPlaceholder] = useState("Search By Guest Name");
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [isCreateVisitorModalOpen, setIsCreateVisitorModalOpen] = useState(false);
  const [sortBy, setSortBy] = useState("visit_created_at");
  const [sortOrder, setSortOrder] = useState("DESC");
  const [loading, setLoading] = useState(false);

  // Fetch guests from API with proper parameters
  const fetchGuests = useCallback(async (params = {}) => {
    setLoading(true);
    try {
      const fetFacilityId = localStorage.getItem("selectedFacility");

      if (!fetFacilityId) {
        setAllGuests([]);
        setDateFilteredGuests([]);
        return;
      }

      // Build API parameters
      const apiParams = {
        search: tableSearchTerm || undefined,
        sortBy: sortBy || "visit_created_at",
        sortOrder: sortOrder || "DESC",
        ...params
      };

      // Add filter-specific parameters based on activeFilter
      if (activeFilter === "recent") {
        apiParams.recent_visitors_time = 120;
      } else if (activeFilter === "invited") {
        apiParams.is_invited = true;
      } else if (activeFilter === "checkedin") {
        apiParams.is_checked_in = true;
      } else if (activeFilter === "checkedout") {
        apiParams.is_checkedout = true;
      } else if (activeFilter === "checkInDenied") {
        apiParams.is_checkin_denied = true;
      }

      // Add date filter if selectedDate is not today
      const today = moment().format("YYYY-MM-DD");
      const selectedDateFormatted = moment(selectedDate).format("YYYY-MM-DD");
      if (selectedDateFormatted !== today) {
        apiParams.checkin_date = selectedDateFormatted;
      }

      // Add host filter if a patient is selected
      if (selectedPatient?.id) {
        apiParams.host_id = selectedPatient.id;
      }

      const response = await getVisitGuests(fetFacilityId, null, apiParams);
      const guests = response?.data?.data || [];
      setAllGuests(guests);
      setDateFilteredGuests(guests);
    } catch (error) {
      console.error("Error fetching guests:", error);
      setAllGuests([]);
      setDateFilteredGuests([]);
    } finally {
      setLoading(false);
    }
  }, [tableSearchTerm, sortBy, sortOrder, activeFilter, selectedDate, selectedPatient]);

  // Single useEffect to handle all data fetching
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchGuests();
    }, tableSearchTerm ? 500 : 0);

    return () => clearTimeout(timeoutId);
  }, [tableSearchTerm, sortBy, sortOrder, activeFilter, selectedDate, selectedPatient, fetchGuests]);

  // Refs for clickOutside logic
  const patientSearchRef = useRef(null);
  const guestSearchRef = useRef(null);
  useClickOutside(guestSearchRef, () => setIsGuestDropdownVisible(false));
  useClickOutside(patientSearchRef, () => setIsDropdownVisible(false));

  // ---------------- Handlers ----------------
  const handleHome = () => {
    setSelectedPatient(null);
    setSelectedHost(null);
    setSelectedGuest(null);
    setPatientSearchPlaceholder("Search By Host Name, EID");
    setGuestSearchPlaceholder("Search By Guest Name");
    setActiveFilter("all");
    setTableSearchTerm("");
    fetchGuests();
  };

  const handleHostSearchInputChange = async (value) => {
    setSearchTerm(value);
    if (value.trim()) {
      try {
        const facilityId = localStorage.getItem("selectedFacility");
        if (!facilityId) {
          setSearchResults([]);
          setIsDropdownVisible(false);
          return;
        }

        const apiResults = await searchVisits({
          facility_id: facilityId,
          search: value,
          type: 1 // 1 = Host
        });
        setSearchResults(apiResults?.data?.data || []);
        setIsDropdownVisible(true);
      } catch (error) {
        console.error("Error searching hosts:", error);
        setSearchResults([]);
        setIsDropdownVisible(false);
      }
    } else {
      setSearchResults([]);
      setIsDropdownVisible(false);
    }
  };

  const handleHostClick = async (host) => {
    console.log("Host clicked:", host);
    setSelectedPatient(host);
    setSelectedGuest(null);
    setIsDropdownVisible(false);
    setSearchTerm("");
    setPatientSearchPlaceholder(host.name || `${host.first_name || ''} ${host.last_name || ''}`.trim());
    setGuestSearchPlaceholder("Search By Guest Name");
    setShowVisitorForm(false);
    
    if (host.visit_id) {
      try {
        const facilityId = localStorage.getItem("selectedFacility");
        console.log("Calling getVisitGuests with:", facilityId, host.visit_id);
        const response = await getVisitGuests(facilityId, host.visit_id);
        console.log("Host API response:", response);
        const hostData = response?.data?.data || host;
        console.log("Setting selectedHost to:", hostData);
        setSelectedHost(hostData);
      } catch (error) {
        console.error("Error fetching host details:", error);
        setSelectedHost(host);
      }
    } else {
      console.log("No visit_id, setting selectedHost to:", host);
      setSelectedHost(host);
    }
    
    fetchGuests({ host_id: host.id });
  };

  const handleVisitorSearchInputChange = async (value) => {
    setGuestSearchTerm(value);
    if (value.trim()) {
      try {
        const facilityId = localStorage.getItem("selectedFacility");
        if (!facilityId) {
          setGuestSearchResults([]);
          setIsGuestDropdownVisible(false);
          return;
        }

        const apiResults = await searchVisits({
          facility_id: facilityId,
          search: value,
          type: 0 // 0 = Guest
        });
        setGuestSearchResults(apiResults?.data?.data || []);
        setIsGuestDropdownVisible(true);
      } catch (error) {
        console.error("Error searching guests:", error);
        setGuestSearchResults([]);
        setIsGuestDropdownVisible(false);
      }
    } else {
      setGuestSearchResults([]);
      setIsGuestDropdownVisible(false);
    }
  };

  const handleGuestClick = async (visitor) => {
    console.log("Guest clicked:", visitor);
    setSelectedHost(null);
    setIsGuestDropdownVisible(false);
    setGuestSearchTerm("");
    setGuestSearchPlaceholder(visitor.visitorName || visitor.guest_name || visitor.name || "Selected Guest");
    setPatientSearchPlaceholder("Search By Host Name, EID");
    setShowVisitorForm(false);
    
    if (visitor.visit_id) {
      try {
        const facilityId = localStorage.getItem("selectedFacility");
        console.log("Calling getVisitGuests with:", facilityId, visitor.visit_id);
        const response = await getVisitGuests(facilityId, visitor.visit_id);
        console.log("Guest API response:", response);
        const guestData = response?.data?.data || visitor;
        console.log("Setting selectedGuest to:", guestData);
        setSelectedGuest(guestData);
      } catch (error) {
        console.error("Error fetching guest details:", error);
        setSelectedGuest(visitor);
      }
    } else {
      console.log("No visit_id, setting selectedGuest to:", visitor);
      setSelectedGuest(visitor);
    }

    const guestInCurrentData = allGuests.find(guest =>
      guest.id === visitor.id ||
      guest.guest_id === visitor.id ||
      guest.guest_name === visitor.visitorName ||
      guest.guest_name === visitor.guest_name ||
      guest.guest_name === visitor.name
    );

    if (guestInCurrentData) {
      setDateFilteredGuests([guestInCurrentData]);
    } else {
      fetchGuests({ guest_id: visitor.id });
    }
  };

  const handleCreateVisitorClick = () => {
    setIsCreateVisitorModalOpen(true);
  };

  const handleCreateVisitorSubmit = (newVisitor) => {
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
    setIsCreateVisitorModalOpen(false);
  };

  const handleImageCaptured = (imageData) => {
    const updatedGuests = dateFilteredGuests.map((guest) =>
      guest.id === selectedGuestId ? { ...guest, image: imageData } : guest
    );
    setAllGuests(updatedGuests);
    setDateFilteredGuests(updatedGuests);
    setIsModalOpen(false);
  };

  const openModal = (title, guestId) => {
    setSelectedGuestId(guestId);
    setIsModalOpen(true);
  };

  const handlePrintClick = (guest) => {
    setSelectedGuest(guest);
    setPrintModalVisible(true);
  };

  const handleClosePrintModal = () => {
    setPrintModalVisible(false);
    setSelectedGuest(null);
  };

  const handleAddVisitor = (newVisitor) => {
    if (selectedPatient) {
      newVisitor.hostName = selectedPatient.name;
      newVisitor.facility = selectedPatient.site;
    }
    const updatedAllGuests = [newVisitor, ...allGuests];
    setAllGuests(updatedAllGuests);
    setDateFilteredGuests(updatedAllGuests);
  };

  const handleHistoryOpen = () => {
    console.log("History panel opened");
  };

  const handleFilterChange = (newFilter) => {
    if (activeFilter !== newFilter) {
      setActiveFilter(newFilter);
    }
  };

  const handleSort = (column, sortDirection) => {
    const columnId = column.id || column.selector || column.name;
    let apiSortBy = columnId;
    
    switch (columnId) {
      case 'guest_name': apiSortBy = 'guest_name'; break;
      case 'host_name': apiSortBy = 'host_name'; break;
      case 'facility_name': apiSortBy = 'facility_name'; break;
      case 'check_in_time': apiSortBy = 'check_in_time'; break;
      case 'check_out_time': apiSortBy = 'check_out_time'; break;
      default: apiSortBy = 'visit_created_at';
    }

    if (sortBy !== apiSortBy || sortOrder !== sortDirection.toUpperCase()) {
      setSortBy(apiSortBy);
      setSortOrder(sortDirection.toUpperCase());
    }
  };

  const filterVisitorsByDate = (date) => {
    const formattedSelectedDate = moment(date).format("M-D-YYYY");
    const filteredVisitors = allGuests.filter((visitor) => {
      const visitorDate = moment(visitor.startDate, "M-D-YYYY h:mm A").format("M-D-YYYY");
      return visitorDate === formattedSelectedDate;
    });
    setDateFilteredGuests(filteredVisitors);
    setShowVisitorForm(false);
  };

  const refreshGuestsData = () => {
    const fetFacilityId = localStorage.getItem("selectedFacility");
    if (fetFacilityId) {
      getVisitGuests(fetFacilityId)
        .then((response) => {
          const guests = response?.data?.data || [];
          setAllGuests(guests);
          setDateFilteredGuests(guests);
        })
        .catch(() => {
          setAllGuests([]);
          setDateFilteredGuests([]);
        });
    }
  };

  const visitorColumns = getVisitorColumns({
    openModal,
    handlePrintClick,
    profileImage: defaultImg,
    onRefresh: refreshGuestsData,
  });
  
  const displayedGuestList = Array.isArray(dateFilteredGuests) ? dateFilteredGuests : [];
  const filterOptions = [
    { value: "recent", label: "Recent visitors" },
    { value: "all", label: "All Visitors" },
    { value: "invited", label: "All Invited" },
    { value: "checkedin", label: "Checked In" },
    { value: "checkedout", label: "Checked Out" },
    { value: "checkInDenied", label: "Check-in-denied" }
  ];

  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      <div className="text-[24px] font-normal text-[#4F2683]">
        <h3>Reception Desk</h3>
      </div>

      <div>
        <div className="flex flex-col sm:flex-row sm:justify-center items-center sm:gap-6 my-4 mb-8">
          <Button type="imgbtn" className="px-[10px] py-2" icon={homeicon} onClick={handleHome} />

          <HostSearch
            placeholder={patientSearchPlaceholder}
            searchTerm={searchTerm}
            onInputChange={(value) => {
              setSearchTerm(value);
              handleHostSearchInputChange(value);
            }}
            results={searchResults}
            onResultClick={handleHostClick}
            isDropdownVisible={isDropdownVisible}
            containerRef={patientSearchRef}
          />

          <VisitorSearch
            placeholder={guestSearchPlaceholder}
            searchTerm={guestSearchTerm}
            onInputChange={handleVisitorSearchInputChange}
            results={guestSearchResults}
            onResultClick={handleGuestClick}
            isDropdownVisible={isGuestDropdownVisible}
            containerRef={guestSearchRef}
            onCreateClick={handleCreateVisitorClick}
          />

          <DateInput
            value={selectedDate}
            onChange={(date) => {
              setSelectedDate(date);
              filterVisitorsByDate(date);
            }}
            placeholder="Select a date"
            className="w-[25%] rounded-md text-[#4F2683]"
          />
        </div>
      </div>

      {(selectedHost || selectedGuest) && (
        <>
          {console.log("Rendering selectedGuest:", selectedHost)}
          <DetailsCard
            OpenPhotoModal={() => setIsModalOpen(true)}
            profileImage={Array.isArray(selectedGuest) ? selectedGuest[0]?.guest_image || defaultImg : selectedGuest?.guest_image || selectedGuest?.image || defaultImg}
            defaultImage={defaultImg}
            name={Array.isArray(selectedGuest) ? selectedGuest[0]?.host_name || "N/A" : selectedHost[0]?.host_name || "N/A"}
            showHistoryButton={false}
            additionalFields={[
              { label: "Email", value: Array.isArray(selectedGuest) ? selectedGuest[0]?.guest_email || "N/A" : selectedHost[0]?.host_name || "N/A" },
              { label: "Phone", value: Array.isArray(selectedGuest) ? selectedGuest[0]?.phone || "N/A" : selectedGuest?.phone || "N/A" },
              { label: "Status", value: Array.isArray(selectedGuest) ? selectedGuest[0]?.host_status || "N/A" : selectedHost[0]?.host_status || "N/A" },
              { label: "Start Date", value: Array.isArray(selectedGuest) ? selectedGuest[0]?.host_start_date || "N/A" : selectedHost[0]?.host_start_date || "N/A" },
              { label: "End Time", value: Array.isArray(selectedGuest) ? (selectedGuest[0]?.check_in_time ? formatDateTime(selectedGuest[0].check_in_time) : selectedHost[0]?.host_end_date || "N/A" ) : "N/A" }
            ]}
          />
        </>
      )}

      {showVisitorForm && (
        <VisitorForm
          onAddGuest={handleAddVisitor}
          onClose={() => setShowVisitorForm(false)}
          hostName={selectedPatient?.name || `${selectedPatient?.first_name || ''} ${selectedPatient?.last_name || ''}`.trim()}
          hostId={selectedPatient?.id}
          selectedHost={selectedPatient}
        />
      )}

      {selectedHost && (
        <>
          <FilterButtons filter={activeFilter} onFilterChange={handleFilterChange} filterOptions={filterOptions} />
          <div className="mt-4">
            {loading ? <Loader /> : (
              <GenericTable
                title={"Guests"}
                searchTerm={tableSearchTerm}
                onSearchChange={setTableSearchTerm}
                onSort={handleSort}
                columns={visitorColumns}
                data={displayedGuestList}
                fixedHeader
                onAdd={() => setShowVisitorForm(true)}
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                showAddButton={true}
                striped={false}
                passValueToSearch={true}
              />
            )}
          </div>
        </>
      )}

      {selectedGuest && (
        <>
          <FilterButtons filter={activeFilter} onFilterChange={handleFilterChange} filterOptions={filterOptions} />
          <div className="mt-4">
            {loading ? <Loader /> : (
              <GenericTable
                title={"Guests"}
                searchTerm={tableSearchTerm}
                onSearchChange={setTableSearchTerm}
                onSort={handleSort}
                columns={visitorColumns}
                data={displayedGuestList}
                fixedHeader
                onAdd={() => setShowVisitorForm(true)}
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                showAddButton={true}
                striped={false}
                passValueToSearch={true}
              />
            )}
          </div>
        </>
      )}

      {!selectedHost && !selectedGuest && (
        <>
          <FilterButtons filter={activeFilter} onFilterChange={handleFilterChange} filterOptions={filterOptions} />
          <div className="mt-4">
            {loading ? <Loader /> : (
              <GenericTable
                title={"Guests"}
                searchTerm={tableSearchTerm}
                onSearchChange={setTableSearchTerm}
                onSort={handleSort}
                columns={visitorColumns}
                data={displayedGuestList}
                fixedHeader
                onAdd={() => setShowVisitorForm(true)}
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                showAddButton={selectedPatient ? true : false}
                striped={false}
                passValueToSearch={true}
              />
            )}
          </div>
        </>
      )}

      {printModalVisible && selectedGuest && (
        <PrintModal guest={selectedGuest} onClose={handleClosePrintModal} />
      )}

      {isModalOpen && (
        <EditPhotoModal onClose={() => setIsModalOpen(false)} onSave={handleImageCaptured} />
      )}

      {isCreateVisitorModalOpen && (
        <CreateVisitorModal
          isOpen={isCreateVisitorModalOpen}
          onClose={() => setIsCreateVisitorModalOpen(false)}
          onSubmit={handleCreateVisitorSubmit}
          onHostSelect={handleHostClick}
        />
      )}
    </div>
  );
};

export default ReceptionDesk;