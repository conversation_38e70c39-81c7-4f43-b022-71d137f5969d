import React, { useState, useMemo } from "react";
import GenericTable from "../../../../Components/GenericTable";
import AddCardForm from "./AddCardForm";
import ViewCardDetails from "./ViewCardDetails";
import PasswordGeneration from "./PasswordGeneration";
import viewicon from "../../../../Images/ViewIcon.svg";
import viewcard from "../../../../Images/ViewCard.svg";
import viewhistory from "../../../../Images/ViewHistory.svg";
import TruncatedRow from "../../../../Components/Tooltip/TrucantedRow";
import TruncatedCell from "../../../../Components/Tooltip/TruncatedCell";

const Cards = () => {
  const [isAddCardOpen, setIsAddCardOpen] = useState(false);
  const [isViewCardOpen, setIsViewCardOpen] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const [isPasswordGenerationOpen, setIsPasswordGenerationOpen] = useState(false);

  const [cards, setCards] = useState([
    {
      cardNumber: "995153",
      cardFormat: "Multitech General",
      cardType: "Permanent",
      activation: "Jul - 20 - 2022",
      deactivation: "Jul - 20 - 2032",
      status: "Valid",
    },
    // Add more card data as needed
  ]);

  const columns = [
    {
      name: <TruncatedCell text="Card Number" />,
      selector: (row) => row.cardNumber,
      cell: (row) => <TruncatedRow text={row.cardNumber} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Card Format" />,
      selector: (row) => row.cardFormat,
      cell: (row) => <TruncatedRow text={row.cardFormat} />,
    },
    {
      name: <TruncatedCell text="Card Type" />,
      selector: (row) => row.cardType,
      cell: (row) => <TruncatedRow text={row.cardType} />,
    },
    {
      name: <TruncatedCell text="Activation" />,
      selector: (row) => row.activation,
      cell: (row) => <TruncatedRow text={row.activation} />,
    },
    {
      name: <TruncatedCell text="Deactivation" />,
      selector: (row) => row.deactivation,
      cell: (row) => <TruncatedRow text={row.deactivation} />,
    },
    {
      name: <TruncatedCell text="Status" />,
      selector: (row) => row.status,
      cell: (row) => (
        <div
          className={
            row.status === "Valid"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683] px-2 py-1 rounded"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F] px-2 py-1 rounded"
          }
        >
          <TruncatedRow text={row.status} />
        </div>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <div className="flex justify-center items-center space-x-2">
          <div className="relative group">
            <img
              src={viewicon}
              alt="View Icon"
              onClick={() => handleViewCard(row)}
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              View Card
            </div>
          </div>
          <div className="relative group">
            <img
              src={viewcard}
              alt="Report Icon"
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              Report Card
            </div>
          </div>
          <div className="relative group">
            <img
              src={viewhistory}
              alt="Update Password Icon"
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
              onClick={handlePasswordGeneration}
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              Update Password
            </div>
          </div>
        </div>
      ),
    },
  ];

  const handleAddCard = () => setIsAddCardOpen(true);
  const handleViewCard = (card) => {
    setSelectedCard(card);
    setIsViewCardOpen(true);
  };
  const handlePasswordGeneration = () => setIsPasswordGenerationOpen(true);

  const generateUniqueCardNumber = () => {
    return Date.now().toString();
  };

  const handleAddCardSubmit = (formData) => {
    const newCard = {
      cardNumber: generateUniqueCardNumber(),
      cardFormat: formData.cardFormat,
      cardType: formData.cardType,
      activation: formData.activationDate,
      deactivation: formData.deactivationDate,
      status: "Pending",
    };

    setCards((prevCards) => [...prevCards, newCard]);
    setIsAddCardOpen(false);
  };

  const filteredData = useMemo(() => {
    if (!tableSearchTerm) return cards;
    return cards.filter((card) =>
      Object.values(card).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(tableSearchTerm.toLowerCase())
      )
    );
  }, [cards, tableSearchTerm]);

  return (
    <div>
      <div className="bg-white rounded-[10px]">
        <GenericTable
          title={"Cards"}
          searchTerm={tableSearchTerm}
          onSearchChange={(e) => setTableSearchTerm(e.target.value)}
          columns={columns}
          data={filteredData}
          onAdd={handleAddCard}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
      {isAddCardOpen && (
        <AddCardForm
          onClose={() => setIsAddCardOpen(false)}
          onAdd={handleAddCardSubmit}
        />
      )}
      {isViewCardOpen && (
        <ViewCardDetails
          card={selectedCard}
          onClose={() => setIsViewCardOpen(false)}
        />
      )}
      {isPasswordGenerationOpen && (
        <PasswordGeneration
          onClose={() => setIsPasswordGenerationOpen(false)}
        />
      )}
    </div>
  );
};

export default Cards;