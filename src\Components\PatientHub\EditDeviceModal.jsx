import React, { useState, useEffect } from "react";
import CustomDropdown from "../Global/CustomDropdown";
import { useSelector } from "react-redux";
import { updateDevice } from "../../api/Device";
import { getKioskGroups } from "../../api/global";
import { toast } from "react-toastify";

// Hooks for fetching dropdown data
import { useBuildingData } from "../../hooks/useBuildingData";
import { useFloorData } from "../../hooks/useFloorData";
import { useRoomData } from "../../hooks/useRoomData";

const EditDeviceModal = ({ open, onClose, onSave, selectedRow, setSelectedRow }) => {
  const [show, setShow] = useState(false);

  // Get current facility from Redux store
  const selectedFacilityId = useSelector(state => state.facility.selectedFacilityId);
  const selectedFacilityName = useSelector(state => state.facility.selectedFacilityName);

  // Building/Floor/Room selection state
  const [selectedBuilding, setSelectedBuilding] = useState('');
  const [selectedFloor, setSelectedFloor] = useState('');
  const [kioskGroups, setKioskGroups] = useState([]);

  // Fetch dropdown options using hooks
  const buildingOptions = useBuildingData(selectedFacilityId);
  const floorOptions = useFloorData(selectedBuilding);
  const roomOptions = useRoomData(selectedFloor);

  console.log("EditDeviceModal: selectedFacilityId from Redux:", selectedFacilityId);
  console.log("EditDeviceModal: buildingOptions:", buildingOptions);

  // Initialize selected values when selectedRow changes
  useEffect(() => {
    if (selectedRow) {
      setSelectedBuilding(selectedRow.building || '');
      setSelectedFloor(selectedRow.floor || '');
    }
  }, [selectedRow]);

  useEffect(() => {
    if (open) {
      setTimeout(() => setShow(true), 10);
      
      // Fetch kiosk groups when modal opens
      getKioskGroups()
        .then((res) => {
          if (res?.status && Array.isArray(res?.data?.data)) {
            setKioskGroups(res.data.data);
          } else {
            setKioskGroups([]);
          }
        })
        .catch((err) => {
          console.error('Error fetching kiosk groups:', err);
          setKioskGroups([]);
        });
    } else {
      setShow(false);
    }
  }, [open]);

  if (!open || !selectedRow) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Edit Device
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form
          onSubmit={async (e) => {
            e.preventDefault();
            // Prepare device data with only the fields needed
            const updatedDevice = {
              ...selectedRow,
              device_id: selectedRow.id  // Keep device_id for API call reference
            };

            // Call parent onSave callback with the updated device
            onSave(updatedDevice);
          }}
          className="bg-white p-6 rounded-lg"
        >
          <div className="flex items-center mb-4">
            <label htmlFor="device_id" className="w-1/4 text-[16px] font-normal">
              Device ID
            </label>
            <div className="w-3/4">
              <input
                id="device_id"
                name="device_id"
                type="text"
                className="w-full border bg-gray-100 rounded p-2 focus:outline-none"
                value={selectedRow.device_id || ''}
                readOnly
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="device" className="w-1/4 text-[16px] font-normal">
              Device
            </label>
            <div className="w-3/4">
              <input
                id="device"
                name="device"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={selectedRow.device}
                onChange={e => setSelectedRow({ ...selectedRow, device: e.target.value })}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="deviceGroup" className="w-1/4 text-[16px] font-normal">
              Device Group*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={kioskGroups.map(group => ({ 
                  value: group.kiosk_group_id, 
                  label: group.name 
                }))}
                value={selectedRow.deviceGroup}
                onSelect={value => setSelectedRow({ ...selectedRow, deviceGroup: value })}
                placeholder="Select Device Group"
                className="w-full h-10"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="facility" className="w-1/4 text-[16px] font-normal">
              Facility*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded bg-gray-100"
                value={selectedFacilityName || 'No facility selected'}
                readOnly
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="building" className="w-1/4 text-[16px] font-normal">
              Building*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={buildingOptions}
                value={selectedBuilding}
                onSelect={(value) => {
                  setSelectedBuilding(value);
                  // Reset dependent fields when building changes
                  setSelectedFloor('');
                  setSelectedRow({ ...selectedRow, building: value, floor: '', room: '' });
                }}
                placeholder="Select Building"
                className="w-full h-10"
                disabled={!selectedFacilityId}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="floor" className="w-1/4 text-[16px] font-normal">
              Floor*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={floorOptions}
                value={selectedFloor}
                onSelect={(value) => {
                  setSelectedFloor(value);
                  // Reset room when floor changes
                  setSelectedRow({ ...selectedRow, floor: value, room: '' });
                }}
                placeholder="Select Floor"
                className="w-full h-10"
                disabled={!selectedBuilding}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="room" className="w-1/4 text-[16px] font-normal">
              Room*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={roomOptions}
                value={selectedRow.room}
                onSelect={(value) => {
                  setSelectedRow({ ...selectedRow, room: value });
                }}
                placeholder="Select Room"
                className="w-full h-10"
                disabled={!selectedFloor}
              />
            </div>
          </div>
          <div className="flex gap-4 justify-end">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditDeviceModal;
