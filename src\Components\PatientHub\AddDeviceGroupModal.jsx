import React, { useState, useEffect } from "react";
import Button from "../Global/Button";
import { getKioskSettings } from "../../api/global";

const AddDeviceGroupModal = ({ isOpen, onClose, onSave, facilityId }) => {
  const [newEntry, setNewEntry] = useState({
    deviceGroup: "",
    assignApp: [],
    status: "Active",
  });
  const [show, setShow] = useState(false);
  const [kioskApps, setKioskApps] = useState([]);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setShow(true), 10);
      fetchKioskApps();
    } else {
      setShow(false);
    }
  }, [isOpen]);

  const fetchKioskApps = async () => {
    try {
      const response = await getKioskSettings();
      const data = response?.data?.data || [];
      if (Array.isArray(data) && data.length > 0) {
        setKioskApps(data);
      } else {
        // Use dummy data if API returns empty or fails
        setKioskApps([
          { kiosk_setting_id: 1, name: "N<PERSON>" },
          { kiosk_setting_id: 2, name: "re-check-in" },
          { kiosk_setting_id: 3, name: "Doctor's Office Visit" },
          { kiosk_setting_id: 4, name: "Expedite Check-in" },
          { kiosk_setting_id: 5, name: "Walk-in Guest" },
          { kiosk_setting_id: 6, name: "Guest Verification" },
        ]);
      }
    } catch (error) {
      console.error("Error fetching kiosk settings:", error);
      // Use dummy data on error
      setKioskApps([
        { kiosk_setting_id: 1, name: "NDA" },
        { kiosk_setting_id: 2, name: "re-check-in" },
        { kiosk_setting_id: 3, name: "Doctor's Office Visit" },
        { kiosk_setting_id: 4, name: "Expedite Check-in" },
        { kiosk_setting_id: 5, name: "Walk-in Guest" },
        { kiosk_setting_id: 6, name: "Guest Verification" },
      ]);
    }
  };

  if (!isOpen) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    onSave(newEntry);
    setNewEntry({ deviceGroup: "", assignApp: [], status: "Active" });
    handleClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white p-6 rounded shadow-lg w-full max-w-3xl h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[30px] font-normal text-[#4F2683]">Add Device Group</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form className="bg-white p-2 rounded-lg" onSubmit={handleFormSubmit}>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device Group*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Device Group"
                value={newEntry.deviceGroup}
                onChange={e => setNewEntry({ ...newEntry, deviceGroup: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Assign App*
            </label>
            <div className="w-3/4">
              <select
                className="border p-2 w-full rounded"
                value=""
                onChange={e => {
                  if (e.target.value && !newEntry.assignApp.includes(e.target.value)) {
                    setNewEntry({ ...newEntry, assignApp: [...newEntry.assignApp, e.target.value] });
                  }
                }}
              >
                <option value="">Select App</option>
                {Array.isArray(kioskApps) && kioskApps.map((app, index) => (
                  <option key={app.kiosk_setting_id || index} value={app.name}>
                    {app.name}
                  </option>
                ))}
              </select>
              {newEntry.assignApp.length > 0 && (
                <div className="mt-2">
                  {newEntry.assignApp.map((app, index) => (
                    <span key={index} className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2 mb-1">
                      {app}
                      <button
                        type="button"
                        className="ml-1 text-red-500"
                        onClick={() => {
                          setNewEntry({ ...newEntry, assignApp: newEntry.assignApp.filter(a => a !== app) });
                        }}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Status
            </label>
            <div className="w-3/4">
              <select
                className="border p-2 w-full rounded"
                value={newEntry.status}
                onChange={e => setNewEntry({ ...newEntry, status: e.target.value })}
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
              </select>
            </div>
          </div>
          <div className="flex justify-center gap-4">
            <Button
              type="cancel"
              onClick={handleClose}
              label="Cancel"
            />
            <Button
              type="primary"
              label="Save"
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDeviceGroupModal;
