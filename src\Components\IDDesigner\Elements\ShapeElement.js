import React, { forwardRef } from 'react';
import { Rect, Circle, Star, Line, RegularPolygon } from 'react-konva';

const ShapeElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const commonProps = {
    ref,
    x: element.x,
    y: element.y,
    offsetX: 0,
    offsetY: 0,
    rotation: element.rotation || 0,
    opacity: element.opacity !== undefined ? element.opacity : 1,
    visible: element.visible !== false,
    draggable: !element.locked && !isPreviewMode,
    onClick,
    onDragEnd,
    fill: element.fill || '#e3f2fd',
    stroke: element.stroke || '#1976d2',
    strokeWidth: element.strokeWidth || 1,
  };

  switch (element.shapeType) {
    case 'rectangle':
      return (
        <Rect
          {...commonProps}
          width={element.width}
          height={element.height}
          cornerRadius={element.cornerRadius || 0}
        />
      );
    
    case 'circle':
      return (
        <Circle
          {...commonProps}
          x={element.x + element.width / 2}
          y={element.y + element.height / 2}
          radius={Math.min(element.width, element.height) / 2}
        />
      );
    
    case 'star':
      return (
        <Star
          {...commonProps}
          x={element.x + element.width / 2}
          y={element.y + element.height / 2}
          numPoints={element.numPoints || 5}
          innerRadius={Math.min(element.width, element.height) / 4}
          outerRadius={Math.min(element.width, element.height) / 2}
        />
      );

    case 'line':
      return (
        <Line
          {...commonProps}
          x={element.x + element.width / 2}
          y={element.y + element.height / 2}
          points={[
            -element.width / 2, 0,
            element.width / 2, 0
          ]}
          strokeWidth={element.strokeWidth || 2}
        />
      );

    case 'triangle':
      return (
        <RegularPolygon
          {...commonProps}
          x={element.x + element.width / 2}
          y={element.y + element.height / 2}
          sides={3}
          radius={Math.min(element.width, element.height) / 2}
        />
      );

    case 'polygon':
      return (
        <RegularPolygon
          {...commonProps}
          x={element.x + element.width / 2}
          y={element.y + element.height / 2}
          sides={element.sides || 6}
          radius={Math.min(element.width, element.height) / 2}
        />
      );

    default:
      return (
        <Rect
          {...commonProps}
          width={element.width}
          height={element.height}
        />
      );
  }
});

ShapeElement.displayName = 'ShapeElement';

export default ShapeElement;
