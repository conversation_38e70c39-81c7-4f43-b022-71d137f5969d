import React, { useState } from "react";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import FilterPanel from "../../Components/Observation/FilterPanel";
import { IoFilter } from "react-icons/io5";
import TruncatedRow from "../../Components/Tooltip/TrucantedRow";
import { HiOutlineHandThumbUp, HiOutlineHandThumbDown } from "react-icons/hi2";
import { initialTasks } from "../../api/static";
import ViewTasks from "../TaskHub/ViewTasks";
import { useTranslation } from "react-i18next";

const TaskPage = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [showView, setShowView] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [filter, setFilter] = useState("All");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);

  const handleFilterOpen = () => setIsFilterPanelOpen(true);

  const [tasks, setTasks] = useState(initialTasks);

  // Update recommendation status using the unique id
  const updateRecStatus = (id, newStatus) => {
    setTasks(prevData =>
      prevData.map(task =>
        task.id === id ? { ...task, recommend: newStatus } : task
      )
    );
  };
  const handleView = (req) => {
    setSelectedRequest(req);
    setShowView(true);
  };

  // Filter tasks based solely on recommendation filter
  const filteredDatas = tasks.filter(row =>
    filter === "All" ? true : row.recommend.toLowerCase() === "approve"
  );

 const columns = [
  {
    name: <TruncatedCell text={t('my_task.task_id')} />,
    selector: row => <TruncatedRow text={row.taskId} />,
    cell: (row) => (
      <span
        className="underline underline-offset-1 cursor-pointer"
        onClick={() => handleView(row)}
      >
        {row.taskId}
      </span>
    ),
    minWidth: "100px",
  },
  {
    name: <TruncatedCell text={t('my_task.type')} />,
    selector: row => row.type,
    cell: row => <TruncatedRow text={row.type} />,
    minWidth: "100px",
  },
  {
    name: <TruncatedCell text={t('my_task.request_for')} />,
    selector: row => row.requestFor,
    cell: row => <TruncatedRow text={row.requestFor} />,
    minWidth: "100px",
  },
  {
    name: <TruncatedCell text={t('my_task.items')} />,
    selector: row => row.items,
    cell: row => <TruncatedRow text={row.items} />,
    minWidth: "120px",
  },
  {
    name: <TruncatedCell text={t('my_task.request_id')} />,
    selector: row => row.requestId,
    cell: row => <TruncatedRow text={row.requestId} />,
    minWidth: "130px",
  },
  {
    name: <TruncatedCell text={t('my_task.requested_by')} />,
    selector: row => <TruncatedRow text={row.requestedBy} />,
    center: true,
    minWidth: "130px",
  },
  {
    name: <TruncatedCell text={t('my_task.created_by')} />,
    selector: row => row.createdBy,
    cell: row => <TruncatedRow text={row.createdBy} />,
    center: true,
    minWidth: "130px",
  },
  {
    name: <TruncatedCell text={t('my_task.assignee')} />,
    selector: row => <TruncatedRow text={row.assignee} />,
    cell: row => <TruncatedRow text={row.assignee} />,
    center: true,
    minWidth: "130px",
  },
  {
    name: <TruncatedCell text={t('my_task.justification')} />,
    selector: row => <TruncatedRow text={row.justification} />,
    cell: row => <TruncatedRow text={row.justification} />,
    center: true,
    minWidth: "180px",
  },
  {
    name: <TruncatedCell text={t('my_task.recommend')} />,
    selector: row => row.recommend,
    cell: row =>
      row.recommend.toLowerCase() === 'approve'
        ? <HiOutlineHandThumbUp className="text-[#4F2683] text-xl" />
        : <HiOutlineHandThumbDown className="text-[#8F8F8F] text-xl" />,
    center: true,
    minWidth: "140px",
  },
  {
    name: t('my_task.actions'),
    cell: row => (
      <div className="flex gap-2">
        <button
          className="flex gap-1 p-1 whitespace-nowrap w-full border text-[#4F2683] rounded-full"
          onClick={() => updateRecStatus(row.id, "Approve")}
        >
          <HiOutlineHandThumbUp className="text-xl" />
          <p>{t('my_task.approve')}</p>
        </button>
        <button
          className="flex gap-1 p-1 whitespace-nowrap w-full border text-[#8F8F8F] rounded-full"
          onClick={() => updateRecStatus(row.id, "Deny")}
        >
          <HiOutlineHandThumbDown className="text-xl" />
          <p>{t('my_task.deny')}</p>
        </button>
      </div>
    ),
    center: true,
    minWidth: "300px",
  },
];


  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">{t('my_task.task_hub')}</h2>
      </div>
      <div className="mb-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={[
            { label: t('my_task.all'), value: "All" },
            { label: t('my_task.active'), value: "Active" },
          ]}
        />
      </div>

      <div className="bg-white rounded-[10px]">
        <GenericTable
          fixedHeaderScrollHeight="420px"
          title={t('my_task.tasks')}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columns={columns}
          data={filteredDatas}
          showSearch={true}
          showAddButton={false}
          Checkboxes={true}
          highlightOnHover={false}
          passValueToSearch={true}
          extraControls={
            <IoFilter
              className="bg-white shadow-sm border items-center p-[5px] text-[#4F2683] h-[32px] w-8 rounded cursor-pointer"
              onClick={handleFilterOpen}
            />
          }
        />
      </div>
      {showView && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewTasks
                  taskData={selectedRequest}
                  onClose={() => setShowView(false)}
              />
            </div>
          </div>
        </div>
      )}
      <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
    </div>
  );
};

export default TaskPage;
