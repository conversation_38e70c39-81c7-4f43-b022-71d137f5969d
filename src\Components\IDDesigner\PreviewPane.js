import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Typography,
  TextField,
  Button,
  Divider,
  Paper,
} from '@mui/material';

import { updateMockData } from '../../redux/idDesignerSlice';

const PreviewPane = () => {
  const dispatch = useDispatch();
  const { mockData } = useSelector((state) => state.idDesigner);

  const handleMockDataChange = (field, value) => {
    dispatch(updateMockData({ [field]: value }));
  };

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
          Preview Data
        </Typography>
      </Box>

      <Box sx={{ p: 2 }}>
        <Typography variant="subtitle2" sx={{ mb: 2 }}>
          Mock Data for Preview
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <TextField
            label="Name"
            value={mockData.name || ''}
            onChange={(e) => handleMockDataChange('name', e.target.value)}
            size="small"
          />

          <TextField
            label="ID"
            value={mockData.id || ''}
            onChange={(e) => handleMockDataChange('id', e.target.value)}
            size="small"
          />

          <TextField
            label="Department"
            value={mockData.department || ''}
            onChange={(e) => handleMockDataChange('department', e.target.value)}
            size="small"
          />

          <TextField
            label="QR Data"
            value={mockData.qrData || ''}
            onChange={(e) => handleMockDataChange('qrData', e.target.value)}
            size="small"
          />

          <TextField
            label="Date"
            value={mockData.date || new Date().toLocaleDateString()}
            onChange={(e) => handleMockDataChange('date', e.target.value)}
            size="small"
          />
        </Box>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Preview Actions
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Button variant="outlined" size="small">
            Export as PNG
          </Button>
          <Button variant="outlined" size="small">
            Export as PDF
          </Button>
          <Button variant="outlined" size="small">
            Print Preview
          </Button>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Available Variables
        </Typography>

        <Paper variant="outlined" sx={{ p: 1, bgcolor: 'grey.50' }}>
          <Typography variant="caption" component="div">
            • {`{{name}}`} - Person's name
          </Typography>
          <Typography variant="caption" component="div">
            • {`{{id}}`} - ID number
          </Typography>
          <Typography variant="caption" component="div">
            • {`{{department}}`} - Department
          </Typography>
          <Typography variant="caption" component="div">
            • {`{{date}}`} - Current date
          </Typography>
          <Typography variant="caption" component="div">
            • {`{{qrData}}`} - QR code data
          </Typography>
        </Paper>
      </Box>
    </Box>
  );
};

export default PreviewPane;
