import React, { useState } from "react";
import GenericTable from "../../../GenericTable";
import TabsWithTables from "./TabsWithTables"; // Import TabsWithTables

const ViewReport = ({ onClose }) => {
  const [show, setShow] = useState(false);
  const [showTabs, setShowTabs] = useState(false); // State to toggle TabsWithTables
  const [selectedRow, setSelectedRow] = useState(null); // State to store clicked row data

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const reportData = [
    { request: "Request ID", type: "12345", reason: "Request ID", createOn: "Request ID", startDate: "Request ID", endDate: "Request ID", status: "Request ID" },
   
  ];

  const handleRowClick = (row) => {
    setSelectedRow(row); // Store clicked row data
    setShowTabs(true); // Show TabsWithTables
  };

  const columns = [
    {
      name: "Request Id",
      selector: (row) => row.request,
      sortable: true,
      cell: (row) => (
        <button
          className="text-blue-500 underline"
          onClick={() => handleRowClick(row)}
        >
          {row.request}
        </button>
      ),
    },
    { name: "Type", selector: (row) => row.type },
    { name: "Reason", selector: (row) => row.reason },
    { name: "Created On", selector: (row) => row.createOn },
    { name: "Start Date", selector: (row) => row.startDate },
    { name: "End Date", selector: (row) => row.endDate },
    { name: "Status", selector: (row) => row.status },
  ];

  if (showTabs) {
    return <TabsWithTables onClose={() => setShowTabs(false)} />;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View Request List</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>

        {/* Content Section */}
        <div className="bg-white">
          <GenericTable
            showAddButton={false}
            columns={columns}
            data={reportData}
            fixedHeader
            fixedHeaderScrollHeight="400px"
            highlightOnHover
            striped
          />
        </div>
      </div>
    </div>
  );
};

export default ViewReport;
