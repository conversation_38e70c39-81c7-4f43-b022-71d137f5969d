import React, { useRef, useEffect, useState } from 'react';
import { Group, Transformer } from 'react-konva';
import { useSelector } from 'react-redux';

import TextElement from './TextElement';
import ImageElement from './ImageElement';
import QRCodeElement from './QRCodeElement';
import BarcodeElement from './BarcodeElement';
import ShapeElement from './ShapeElement';

const DesignElement = ({
  element,
  isSelected,
  isPreviewMode,
  onDragMove,
  onClick,
}) => {
  const transformerRef = useRef();
  const elementRef = useRef();
  const [isHovered, setIsHovered] = useState(false);



  useEffect(() => {
    if (isSelected && transformerRef.current && elementRef.current) {
      transformerRef.current.nodes([elementRef.current]);
      transformerRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  const handleDragEnd = (e) => {
    const node = e.target;
    const newPos = {
      x: node.x(),
      y: node.y(),
    };
    onDragMove(newPos);
  };

  const handleTransformEnd = () => {
    const node = elementRef.current;
    if (!node) return;

    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Calculate new dimensions
    const newWidth = Math.max(5, node.width() * scaleX);
    const newHeight = Math.max(5, node.height() * scaleY);

    // Reset scale and apply to width/height
    node.scaleX(1);
    node.scaleY(1);

    const newAttrs = {
      x: node.x(),
      y: node.y(),
      width: newWidth,
      height: newHeight,
      rotation: node.rotation(),
    };

    // For text elements with autoResize disabled, preserve fontSize
    if (element.type === 'text' && element.autoResize === false && element.fontSize) {
      newAttrs.fontSize = element.fontSize;
    }

    // Update element through the provided callback
    onDragMove(newAttrs);
  };

  const renderElement = () => {
    const commonProps = {
      ref: elementRef,
      element: element,
      isPreviewMode: isPreviewMode,
      onClick: onClick,
      onDragEnd: handleDragEnd,
      onMouseEnter: () => setIsHovered(true),
      onMouseLeave: () => setIsHovered(false),
      isHovered: isHovered,
    };

    switch (element.type) {
      case 'text':
        return <TextElement {...commonProps} />;
      case 'image':
        return <ImageElement {...commonProps} />;
      case 'qrcode':
        return <QRCodeElement {...commonProps} />;
      case 'barcode':
        return <BarcodeElement {...commonProps} />;
      case 'shape':
        return <ShapeElement {...commonProps} />;
      default:
        return null;
    }
  };

  return (
    <Group>
      {renderElement()}
      
      {(isSelected || isHovered) && !isPreviewMode && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={(oldBox, newBox) => {
            // Limit resize
            if (newBox.width < 5 || newBox.height < 5) {
              return oldBox;
            }
            return newBox;
          }}
          onTransformEnd={handleTransformEnd}
          enabledAnchors={[
            'top-left',
            'top-center',
            'top-right',
            'middle-right',
            'bottom-right',
            'bottom-center',
            'bottom-left',
            'middle-left',
          ]}
          rotateEnabled={true}
          rotateAnchorOffset={25}
          borderEnabled={true}
          borderStroke={isSelected ? "#0066cc" : "#999999"}
          borderStrokeWidth={isSelected ? 2 : 1}
          borderDash={isSelected ? [4, 4] : [2, 2]}
          anchorFill="#ffffff"
          anchorStroke={isSelected ? "#0066cc" : "#999999"}
          anchorStrokeWidth={2}
          anchorSize={isSelected ? 8 : 6}
          anchorCornerRadius={4}
          rotateAnchorCursor="grab"
          centeredScaling={false}
          keepRatio={false}
          visible={isSelected || isHovered}
          listening={isSelected}
        />
      )}
    </Group>
  );
};

export default DesignElement;
