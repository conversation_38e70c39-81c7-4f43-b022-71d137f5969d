import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import GenericTable from "../GenericTable";
import Swal from "sweetalert2";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FiEdit2 } from "react-icons/fi";
import { MdDelete } from "react-icons/md";
import AddDeviceGroupModal from "./AddDeviceGroupModal";
import EditDeviceGroupModal from "./EditDeviceGroupModal";
import ViewDeviceGroupModal from "./ViewDeviceGroupModal";
import {
  getDeviceGroups,
  deleteDeviceGroup,
  updateDeviceGroup,
  createDeviceGroups
} from "../../api/device_group";

const DeviceGroup = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddModalOpen, setAddModalOpen] = useState(false);
  const [isEditModalOpen, setEditModalOpen] = useState(false);
  const [isViewModalOpen, setViewModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [viewRow, setViewRow] = useState(null);
  const [loading, setLoading] = useState(false);

  // Get kiosk group ID from URL params or fallback to facility ID from Redux store
  const { kioskGroupId } = useParams();
  const selectedFacilityId = useSelector(state => state.facility.selectedFacilityId);

  // Use kioskGroupId from URL if available, otherwise use selectedFacilityId
  const effectiveKioskGroupId = kioskGroupId || selectedFacilityId;

  // Load device groups from API
  useEffect(() => {
    fetchDeviceGroups();
  }, [effectiveKioskGroupId]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchDeviceGroups = async () => {
    setLoading(true);

    console.log("DeviceGroup: Using kiosk group ID:", effectiveKioskGroupId); // Debug log

    if (!effectiveKioskGroupId) {
      // Load dummy data when no kiosk group ID is available
      const dummyData = [
        { id: 1, deviceGroup: "group 1", assignApp: "NDA, re-check-in", status: "Active" },
        { id: 2, deviceGroup: "group 2", assignApp: "Doctor's Office Visit", status: "Active" },
        { id: 3, deviceGroup: "group 3", assignApp: "Expedite Check-in", status: "Active" },
        { id: 4, deviceGroup: "group 4", assignApp: "Walk-in Guest", status: "Inactive" },
        { id: 5, deviceGroup: "group 5", assignApp: "Guest Verification", status: "Active" },
      ];
      setData(dummyData);
      setFilteredData(dummyData);
      setLoading(false);
      return;
    }

    try {
      console.log("DeviceGroup: Calling API with facility ID:", effectiveKioskGroupId); // Debug log
      const response = await getDeviceGroups(effectiveKioskGroupId);

      // Check different possible response structures
      let deviceGroupsData = [];
      if (response?.data?.data) {
        deviceGroupsData = response.data.data;
      } else if (response?.data) {
        deviceGroupsData = Array.isArray(response.data) ? response.data : [];
      } else if (Array.isArray(response)) {
        deviceGroupsData = response;
      }

      if (deviceGroupsData.length === 0) {
        // If no data from API, use dummy data
        const dummyData = [
          { id: 1, deviceGroup: "group 1", assignApp: "NDA, re-check-in", status: "Active" },
          { id: 2, deviceGroup: "group 2", assignApp: "Doctor's Office Visit", status: "Active" },
        ];
        setData(dummyData);
        setFilteredData(dummyData);
      } else {
        // Transform API data to match component structure
        const transformedData = deviceGroupsData.map(item => ({
          id: item.kiosk_group_setting_id || item.id || Math.random(),
          deviceGroup: item.device_group || item.deviceGroup || 'Unknown Group',
          assignApp: Array.isArray(item.assign_app) ? item.assign_app.join(", ") : (item.assign_app || item.assignApp || 'No App'),
          status: item.status || 'Active',
          originalData: item // Keep original data for API calls
        }));


        setData(transformedData);
        setFilteredData(transformedData);
      }
    } catch (error) {
      console.error("Error fetching device groups:", error);
      console.error("Error response:", error.response?.data); // Debug log
      console.error("Error status:", error.response?.status); // Debug log

      // On API error, use dummy data
      const dummyData = [
        { id: 1, deviceGroup: "group 1", assignApp: "NDA, re-check-in", status: "Active" },
        { id: 2, deviceGroup: "group 2", assignApp: "Doctor's Office Visit", status: "Active" },
      ];
      setData(dummyData);
      setFilteredData(dummyData);
      toast.error(`Failed to load device groups: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Search handler
  const handleSearch = e => setSearchTerm(e.target.value);

  // Filter whenever data or searchTerm changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredData(data);
    } else {
      const term = searchTerm.trim().toLowerCase();
      setFilteredData(
        data.filter(row =>
          row.deviceGroup.toLowerCase().includes(term) ||
          row.assignApp.toLowerCase().includes(term) ||
          row.status.toLowerCase().includes(term)
        )
      );
    }
  }, [searchTerm, data]);

  const handleAdd = () => {
    setAddModalOpen(true);
  };

  const handleEdit = row => {
    setSelectedRow(row);
    setEditModalOpen(true);
  };

  const handleView = row => {
    setViewRow(row);
    setViewModalOpen(true);
  };

  const handleDelete = async (id) => {
    const deviceGroupToDelete = data.find(item => item.id === id);
    if (!deviceGroupToDelete) {
      toast.error("Device group not found");
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this device group?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          // Only call API if kiosk group ID is available
          if (effectiveKioskGroupId) {
            console.log("Deleting device group with facility ID:", effectiveKioskGroupId, "and group ID:", id);
            await deleteDeviceGroup(effectiveKioskGroupId, id);
          }
          const updated = data.filter(item => item.id !== id);
          setData(updated);
          setFilteredData(updated);
          toast.success("Device group deleted successfully!");
        } catch (error) {
          console.error("Error deleting device group:", error);
          console.error("Delete error response:", error.response?.data);
          console.error("Delete error status:", error.response?.status);
          // Still remove from local state even if API fails
          const updated = data.filter(item => item.id !== id);
          setData(updated);
          setFilteredData(updated);
          toast.error(`Failed to delete from server: ${error.response?.data?.message || error.message}`);
        }
      }
    });
  };

  // When AddDeviceGroupModal calls onSave
  const handleAddSave = async (newDeviceGroup) => {
    // ensure unique ID in a real app you’d get this from the backend
    try {
      // Only call API if kiosk group ID is available
      if (effectiveKioskGroupId) {
        // Format data with required kiosk_setting_id from assign_app
        const settings = Array.isArray(newDeviceGroup.assignApp) ? newDeviceGroup.assignApp : [newDeviceGroup.assignApp];
        const groupData = {
          settings: settings.map(app => ({
            kiosk_setting_id: typeof app === 'object' ? app.id : app, // Handle both object and string formats
            config_value: "true"
          }))
        };

        console.log("Creating device group with data:", groupData);
        console.log("Using kiosk group ID:", effectiveKioskGroupId);
        await createDeviceGroups(effectiveKioskGroupId, groupData);
        // Refresh the data from API
        fetchDeviceGroups();
      } else {
        // Add to local state if no kiosk group ID available
        const withId = { id: Date.now(), ...newDeviceGroup };
        const updated = [withId, ...data];
        setData(updated);
        setFilteredData(updated);
      }

      setAddModalOpen(false);
      toast.success("Device group added successfully!");
    } catch (error) {
      console.error("Error creating device group:", error);
      console.error("Add error response:", error.response?.data);
      console.error("Add error status:", error.response?.status);
      // Add to local state even if API fails
      const withId = { id: Date.now(), ...newDeviceGroup };
      const updated = [withId, ...data];
      setData(updated);
      setFilteredData(updated);
      setAddModalOpen(false);
      toast.error(`Failed to save to server: ${error.response?.data?.message || error.message}`);
    }
  };

  // When EditDeviceGroupModal calls onSave
  const handleEditSave = async (updatedRow) => {
    try {
      // Only call API if kiosk group ID is available
      if (effectiveKioskGroupId) {
        const settings = Array.isArray(updatedRow.assignApp) ? updatedRow.assignApp : [updatedRow.assignApp];
        const updateData = {
          settings: settings.map(app => ({
            kiosk_setting_id: typeof app === 'object' ? app.id : app, // Handle both object and string formats
            config_value: "true"
          }))
        };

        console.log("Updating device group with data:", updateData);
        console.log("Using kiosk group ID:", effectiveKioskGroupId);
        await updateDeviceGroup(effectiveKioskGroupId, updatedRow.id, updateData);
        // Refresh the data from API
        fetchDeviceGroups();
      } else {
        // Update local state if no kiosk group ID available
        const updated = data.map(item =>
          item.id === updatedRow.id ? updatedRow : item
        );
        setData(updated);
        setFilteredData(updated);
      }

      setEditModalOpen(false);
      toast.success("Device group updated successfully!");
    } catch (error) {
      console.error("Error updating device group:", error);
      console.error("Update error response:", error.response?.data);
      console.error("Update error status:", error.response?.status);
      // Update local state even if API fails
      const updated = data.map(item =>
        item.id === updatedRow.id ? updatedRow : item
      );
      setData(updated);
      setFilteredData(updated);
      setEditModalOpen(false);
      toast.error(`Failed to update on server: ${error.response?.data?.message || error.message}`);
    }
  };

  const columns = [
    {
      id: "deviceGroup",
      name: "Device Group",
      selector: row => row.deviceGroup,
      sortable: true,
      cell: row => (
        <span
          className="text-[#4F2683] underline cursor-pointer"
          onClick={() => handleView(row)}
        >
          {row.deviceGroup}
        </span>
      ),
    },
    {
      id: "assignApp",
      name: "Assign App",
      selector: row => row.assignApp,
      sortable: true,
    },
    {
      id: "status",
      name: "Status",
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span
          className={`px-3 py-1 rounded-full text-xs font-semibold ${
            row.status === "Active"
              ? "bg-[#F4F2F7] text-[#4F2683]"
              : "bg-gray-200 text-gray-500"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      button: true,
      ignoreRowClick: true,
      center: true,
      allowOverflow: true,
      cell: row => (
        <div className="flex items-center gap-2">
          <FiEdit2
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#4F2683]"
            title="Edit"
            onClick={() => handleEdit(row)}
          />
          <MdDelete
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#E74C3C]"
            title="Delete"
            onClick={() => handleDelete(row.id)}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="relative pt-20 ps-20">
      <div className="bg-white rounded-[10px] ">
        <GenericTable
          title="Device Group"
          searchTerm={searchTerm}
          onSearchChange={handleSearch}
          onAdd={handleAdd}
          columns={columns}
          data={filteredData}
          showSearch
          showAddButton
          loading={loading}
        />
      </div>

      <ToastContainer />

      <AddDeviceGroupModal
        isOpen={isAddModalOpen}
        onClose={() => setAddModalOpen(false)}
        onSave={handleAddSave}
        facilityId={effectiveKioskGroupId}
      />

      <EditDeviceGroupModal
        isOpen={isEditModalOpen}
        selectedRow={selectedRow}
        onClose={() => setEditModalOpen(false)}
        onSave={handleEditSave}
        facilityId={effectiveKioskGroupId}
      />

      <ViewDeviceGroupModal
        isOpen={isViewModalOpen}
        row={viewRow}
        onClose={() => setViewModalOpen(false)}
      />
    </div>
  );
};

export default DeviceGroup;
